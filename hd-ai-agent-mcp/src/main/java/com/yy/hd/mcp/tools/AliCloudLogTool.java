package com.yy.hd.mcp.tools;

import com.google.common.base.Splitter;
import com.yy.hd.commons.utils.JsonUtils;
import com.yy.hd.mcp.service.bos.BosService;
import com.yy.hd.mcp.service.logs.AliCloudLog;
import com.yy.hd.mcp.service.logs.AliCloudLogParseResult;
import com.yy.hd.mcp.service.logs.AliCloudLogService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@Component
public class AliCloudLogTool {

    private final BosService bosService;

    private final AliCloudLogService aliCloudLogService;

    private final ResourceLoader resourceLoader;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    private static final String BOS_BUCKET = "zhuiya";

    private static final String LOG_FILE_SUFFIX = ".html";

    private static final Charset UTF_8 = StandardCharsets.UTF_8;

    private static final String HTML_TEMPLATE = "classpath:alicloud_logs.html";

    private static final String LOG_CONTENT_TAG = "<log-entry/>";

    @Tool(name = "AliCloudLogTool", description = "阿里云日志", returnDirect = true)
    public String aliCloudLogTool(@ToolParam(description = "服务") String server,
                                   @ToolParam(description = "搜索关键字") String query,
                                   @ToolParam(description = "查询开始日期，格式：yyyy-MM-dd HH:mm:ss", required = false) String from,
                                   @ToolParam(description = "查询结束的日期，格式：yyyy-MM-dd HH:mm:ss", required = false) String to) {
        log.info("aliCloudLogTool req, server:{}, query:{}, from:{}, to:{}", server, query, from, to);
        File logFile = null;
        try {
            if (!aliCloudLogService.checkServer(server)) {
                List<String> serverList = aliCloudLogService.getServerList();
                return "目前支持查询的服务：" + JsonUtils.toJson(serverList);
            }
            AliCloudLogParseResult aliCloudLogParseResult = aliCloudLogService.getAliCloudLogs(server, query, from, to);
            List<AliCloudLog> logs = aliCloudLogParseResult.logs();
            if (CollectionUtils.isEmpty(logs)) {
                return "没有查询到云日志";
            }
            String fileNameprefix = server + "_" + LocalDateTime.now().format(DATE_TIME_FORMATTER);
            String htmlContent = renderHtmlTemplate(query, aliCloudLogParseResult.terms(), logs);
            logFile = File.createTempFile(fileNameprefix, LOG_FILE_SUFFIX);
            FileUtils.writeStringToFile(logFile, htmlContent, UTF_8);
            String logUrl = bosService.uploadMinFileData(FileUtils.readFileToByteArray(logFile), logFile.getName(), BOS_BUCKET);
            /*
             * logUrl：https://zhuiya.bs2cdn.yy.com/log/zhuiya-server_202506062338135526129360558693390.html
             * url做掩码处理，避免直接暴露日志内容
             */
            return "查询阿里云日志成功，请查看日志详情：{}" + logUrl.replace("yy", "**");
        } catch (IllegalArgumentException e) {
            log.warn("aliCloudLogTool error, msg:{}", e.getMessage());
            return e.getMessage();
        } catch (Exception e) {
            log.error("aliCloudLogTool error", e);
        } finally {
            if (logFile != null) {
                logFile.delete();
            }
        }
        return "查询阿里云日志失败";
    }

    private String renderHtmlTemplate(String query, List<String> terms, List<AliCloudLog> logs) throws Exception {
        Map<String, String> keywords = Splitter.on(" ")
                .splitToList(query)
                .stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toMap(String::toLowerCase, Function.identity(), (a,b) -> b));
        for (AliCloudLog aliCloudLog : logs) {
            String trace = aliCloudLog.getTrace();
            String message = aliCloudLog.getMessage();
            for (String term : terms) {
                if (StringUtils.containsIgnoreCase(trace, term)) {
                    trace = StringUtils.replaceIgnoreCase(trace, term, "<span style=\"color: #ff0000\">" + keywords.getOrDefault(term, term) + "</span>");
                }
                if (StringUtils.containsIgnoreCase(message, term)) {
                    message = StringUtils.replaceIgnoreCase(message, term, "<span style=\"color: #ff0000\">" + keywords.getOrDefault(term, term) + "</span>");
                }
                aliCloudLog.setTrace(trace);
                aliCloudLog.setMessage(message);
            }
        }
        int line = 1;
        StringBuilder logContents = new StringBuilder();
        for (AliCloudLog aliCloudLog : logs) {
            logContents.append("<div class=\"log-container\">")
                    .append("<div class=\"log-content-wrapper\">")
                    .append("<div class=\"log-header\">")
                    .append("<span class=\"")
                    .append(aliCloudLog.getLevel().toLowerCase())
                    .append("\">")
                    .append(aliCloudLog.getLevel())
                    .append("</span>")
                    .append("<span class=\"log-timestamp\">")
                    .append(aliCloudLog.getTime())
                    .append("</span>")
                    .append("</div>")
                    .append("<span class=\"log-id\">")
                    .append(aliCloudLog.getTrace())
                    .append("</span>")
                    .append("<div class=\"log-content\">")
                    .append("<div class=\"log-message\">")
                    .append(aliCloudLog.getMessage())
                    .append("</div>")
                    .append("</div>")
                    .append("</div>")
                    .append("</div>");
        }
        Resource resource = resourceLoader.getResource(HTML_TEMPLATE);
        String htmlTemplate = new String(Files.readAllBytes(Paths.get(resource.getURI())));
        return htmlTemplate.replace(LOG_CONTENT_TAG, logContents);
    }

}