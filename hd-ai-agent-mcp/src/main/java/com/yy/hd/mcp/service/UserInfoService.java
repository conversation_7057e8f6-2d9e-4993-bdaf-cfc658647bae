package com.yy.hd.mcp.service;

import com.yy.hd.commons.uri.HttpUris;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@AllArgsConstructor
@Service
public class UserInfoService {

    private static final String QUERY_UID_URL = HttpUris.QUERY_UID_URI;

    private static final String QUERY_YY_URL = HttpUris.QUERY_YY_URI;

    private final RestTemplate restTemplate;

    public long getUidByYY(String yy) {
        Response response = restTemplate.getForObject(QUERY_UID_URL + yy, Response.class);
        return Long.parseLong(response.getData().toString());
    }

    public long getYYByUid(long uid) {
        Response response = restTemplate.getForObject(QUERY_YY_URL + uid, Response.class);
        return Long.parseLong(response.getData().toString());
    }

    @Data
    static class Response<T> {

        private Integer result;

        private String msg;

        private T data;
    }
}
