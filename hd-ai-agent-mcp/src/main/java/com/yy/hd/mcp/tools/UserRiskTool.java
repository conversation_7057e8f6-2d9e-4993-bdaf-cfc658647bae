package com.yy.hd.mcp.tools;


import com.fasterxml.jackson.core.type.TypeReference;
import com.yy.hd.commons.Response;
import com.yy.hd.commons.uri.HttpUris;
import com.yy.hd.commons.utils.JsonUtils;
import com.yy.hd.commons.utils.MD5Utils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@AllArgsConstructor
@Component
public class UserRiskTool {

    private final RestTemplate restTemplate;

    private static final String QUERY_URL = HttpUris.QUERY_USER_RISK_URI;

    private static final int LAST_FEW_DAYS = 3;

    private static final int MAX_SIZE = 30;

    @Tool(name = "UserRiskTool", description = "用户最近3天的风控数据")
    public String userRiskTool(@ToolParam(description = "用户uid") long uid) {
        String source = uid + "_" + LAST_FEW_DAYS;
        String sign = MD5Utils.getMD5(source.getBytes());
        String url = QUERY_URL.formatted(uid, LAST_FEW_DAYS, sign);
        try {
            String response = restTemplate.getForObject(url, String.class);
            log.info("userRiskTool rsp:{}", response);
            Response<List<SimpleUserRiskLog>> rsp = JsonUtils.fromJson(response, new TypeReference<Response<List<SimpleUserRiskLog>>>() {});
            if (rsp.getResult() != 0) {
                return "查询用户风控数据失败：" + rsp.getMsg();
            }
            List<SimpleUserRiskLog> riskLogs = rsp.getData();
            if (CollectionUtils.isEmpty(riskLogs)) {
                return "查询已完成，用户没有风控数据";
            }
            riskLogs = riskLogs.stream().limit(MAX_SIZE).toList();
            return JsonUtils.toJson(riskLogs);
        } catch (RestClientException e) {
            log.error("userRiskTool fail", e);
            return "查询用户风控数据失败";
        }
    }

    @Data
    public static class SimpleUserRiskLog {

        /**
         * app
         */
        private String app;

        /**
         * 场景
         */
        private String scene;

        /**
         * 子场景
         */
        private String subScene;

        /**
         * 定级结果
         */
        private String riskRate;

        /**
         * 定级原因
         */
        private String riskClassDesc;

        /**
         * 处罚结果
         */
        private String punish;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;
    }
}
