package com.yy.hd.mcp.tools;

import com.yy.hd.commons.uri.HttpUris;
import com.yy.hd.mcp.service.UserInfoService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Slf4j
@AllArgsConstructor
@Component
public class YyPointsTool {

    private final RestTemplate restTemplate;

    private final UserInfoService userInfoService;

    private static final String QUERY_URL = HttpUris.QUERY_YY_POINTS_URI;

    @Tool(name = "YyPointsTool", description = "用户的在线时长")
    public String yyPointsTool(@ToolParam(description = "用户yy", required = false) String yy,
                                   @ToolParam(description = "用户uid", required = false) String uid,
                                   @ToolParam(description = "日期格式：yyyyMMdd", required = false) String queryDate) {
        log.info("yyPointsTool, yy:{}, uid:{}, queryDate:{}", yy, uid, queryDate);
        if (StringUtils.isBlank(yy) && StringUtils.isBlank(uid)) {
            return "请输入用户yy号或uid";
        }
        long yyUid = 0L;
        if (StringUtils.isNotBlank(yy)) {
            yyUid = userInfoService.getUidByYY(yy);
        } else if (StringUtils.isNotBlank(uid)) {
            yyUid = Long.parseLong(uid);
        }
        //默认一天前
        String date = Optional.ofNullable(queryDate).orElse(LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        try {
            return restTemplate.getForObject(QUERY_URL, String.class, yyUid+"", date);
        } catch (Exception e) {
            log.error("yyPointsTool fail", e);
            return "查询用户在线时长失败";
        }
    }
}
