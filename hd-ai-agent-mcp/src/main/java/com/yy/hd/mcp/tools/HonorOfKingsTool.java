package com.yy.hd.mcp.tools;

import com.yy.hd.commons.uri.HttpUris;
import com.yy.hd.commons.utils.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Slf4j
@AllArgsConstructor
@Component
public class HonorOfKingsTool {

    private final RestTemplate restTemplate;

    private static final String QUERY_URL = HttpUris.QUERY_HONOR_OF_KINGS_URI;

    @Tool(name = "HonorOfKingsTool", description = "王者荣耀赏金赛信息活动（赛事状态、退票状态、赏金状态）")
    public String honorOfKingsTool(@ToolParam(description = "用户yy号，非uid") String yy) {
        String url = QUERY_URL + "yy " + yy;
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<HonorOfKingsResponse> httpEntity = new HttpEntity<>(httpHeaders);
        try {
            ResponseEntity<HonorOfKingsResponse> response = restTemplate.exchange(url, HttpMethod.POST,
                    httpEntity, HonorOfKingsResponse.class);
            log.info("honorOfKingsTool rsp:{}", JsonUtils.toJson(response));
            HonorOfKingsResponse responseBody = response.getBody();
            return JsonUtils.toJson(responseBody.getData());
        } catch (RestClientException e) {
            log.error("honorOfKingsTool fail", e);
            return "查询王者荣耀赏金赛信息失败";
        }
    }

    @Data
    static class HonorOfKingsResponse {
        private Integer code;
        private String msg;
        private List<String> data;
    }
}
