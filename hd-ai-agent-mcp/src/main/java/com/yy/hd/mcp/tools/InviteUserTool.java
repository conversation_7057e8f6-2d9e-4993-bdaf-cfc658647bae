package com.yy.hd.mcp.tools;

import com.yy.hd.commons.uri.HttpUris;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

@Slf4j
@AllArgsConstructor
@Component
public class InviteUserTool {

    private final RestTemplate restTemplate;

    private static final String QUERY_URL = HttpUris.QUERY_INVITE_USER_URI;

    @Tool(name = "InviteUserTool", description = "邀好友得限定王者皮肤活动")
    public String inviteUserTool(@ToolParam(description = "uid，邀请人", required = false) String uid,
                                 @ToolParam(description = "invitedUid，被邀请人", required = false) String invitedUid,
                                 @ToolParam(description = "查询条数，默认30条，不用询问", required = false) Integer limit) {
        log.info("inviteUserTool req, uid:{}, invitedUid:{}, limit:{}", uid, invitedUid, limit);
        if (StringUtils.isBlank(uid) && StringUtils.isBlank(invitedUid)) {
            return "请提供uid或者invitedUid";
        }
        if (limit == null || limit <= 0) {
            limit = 30;
        }
        StringBuilder queryString = new StringBuilder("?");
        if (StringUtils.isNotBlank(uid)) {
            queryString.append("uid=").append(uid);
        }
        if (StringUtils.isNotBlank(invitedUid)) {
            if (!queryString.isEmpty()) {
                queryString.append("&");
            }
            queryString.append("invitedUid=").append(invitedUid);
        }
        queryString.append("&limit=").append(limit);
        try {
            String url = QUERY_URL + queryString;
            log.info("inviteUserTool url:{}", url);
            return restTemplate.getForObject(url, String.class);
        } catch (RestClientException e) {
            log.error("inviteUserTool fail", e);
            return "查询邀好友得限定王者皮肤活动数据失败";
        }
    }

}
