package com.yy.hd.mcp.config;

import com.yy.hd.mcp.mysql.DatasourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DatasourceConfig {

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource")
    public DatasourceProperties datasourceProperties() {
        return new DatasourceProperties();
    }

}
