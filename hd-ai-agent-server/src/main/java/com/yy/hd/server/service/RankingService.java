package com.yy.hd.server.service;

import com.yy.hd.commons.uri.HttpUris;
import com.yy.hd.server.dto.BM25SearchDTO;
import com.yy.hd.server.dto.DocumentDTO;
import com.yy.hd.server.dto.FineRankingDTO;
import com.yy.hd.server.dto.SimilaritySearchDTO;
import com.yy.hd.server.dto.UpdateDocumentsDTO;
import org.springframework.ai.document.Document;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

@Service
public class RankingService {

    private final RestTemplate loadBalancedRestTemplate;

    public RankingService(RestTemplateBuilder loadBalancedRestTemplateBuilder) {
        this.loadBalancedRestTemplate = loadBalancedRestTemplateBuilder.build();
    }

    public void documents(String searchKey, List<Document> documents) {
        UpdateDocumentsDTO body = new UpdateDocumentsDTO(searchKey, convert2DTO(documents));
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<UpdateDocumentsDTO> httpEntity = new HttpEntity<>(body, httpHeaders);
        loadBalancedRestTemplate.postForObject(HttpUris.RANKING_DOCUMENTS_URI, httpEntity, String.class);
    }

    public List<DocumentDTO> bm25Search(String searchKey, String query, int topN) {
        BM25SearchDTO body = new BM25SearchDTO(searchKey, query, topN);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<BM25SearchDTO> httpEntity = new HttpEntity<>(body, httpHeaders);
        ResponseEntity<List<DocumentDTO>> responseEntity = loadBalancedRestTemplate.exchange(HttpUris.RANKING_BM25_SEARCH_URI, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<List<DocumentDTO>>() {}, Map.of());
        return responseEntity.getBody();
    }

    public List<DocumentDTO> similaritySearch(String searchKey, String query, double similarity, int topK, String filterExpression) {
        SimilaritySearchDTO body = new SimilaritySearchDTO(searchKey, query, similarity, topK, filterExpression);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<SimilaritySearchDTO> httpEntity = new HttpEntity<>(body, httpHeaders);
        ResponseEntity<List<DocumentDTO>> responseEntity = loadBalancedRestTemplate.exchange(HttpUris.RANKING_SIMILARITY_SEARCH_URI, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<List<DocumentDTO>>() {}, Map.of());
        return responseEntity.getBody();
    }

    public List<DocumentDTO> fineRanking(String query, List<DocumentDTO> documents, double score, int limit) {
        FineRankingDTO body = new FineRankingDTO(query, documents, score, limit);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<FineRankingDTO> httpEntity = new HttpEntity<>(body, httpHeaders);
        ResponseEntity<List<DocumentDTO>> responseEntity = loadBalancedRestTemplate.exchange(HttpUris.RANKING_FINE_RANKING_URI, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<List<DocumentDTO>>() {}, Map.of());
        return responseEntity.getBody();
    }

    private List<DocumentDTO> convert2DTO(List<Document> documents) {
        return documents.stream()
                .map(document -> new DocumentDTO(document.getId(), document.getText(), document.getMetadata(), document.getScore()))
                .toList();
    }

}
