package com.yy.hd.server.config;

import com.alibaba.cloud.ai.graph.CompiledGraph;
import com.alibaba.cloud.ai.graph.GraphRepresentation;
import com.alibaba.cloud.ai.graph.StateGraph;
import com.alibaba.cloud.ai.graph.exception.GraphStateException;
import com.yy.hd.server.graph.WorkflowContext;
import com.yy.hd.server.graph.WorkflowRegisterKeys;
import com.yy.hd.server.graph.node.LLMNode;
import com.yy.hd.server.graph.node.RankingContextNode;
import com.yy.hd.server.graph.node.WorkflowContextNode;
import com.yy.hd.server.graph.node.ToolRetrieverNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.alibaba.cloud.ai.graph.StateGraph.END;
import static com.alibaba.cloud.ai.graph.StateGraph.START;
import static com.alibaba.cloud.ai.graph.action.AsyncEdgeAction.edge_async;
import static com.alibaba.cloud.ai.graph.action.AsyncNodeAction.node_async;

@Slf4j
@Configuration
public class WorkflowConfig {

    @Bean
    public CompiledGraph workflowGraph(WorkflowContextNode workflowContextNode, RankingContextNode rankingContextNode,
                                       ToolRetrieverNode toolRetrieverNode, LLMNode llmNode) throws GraphStateException {
        StateGraph stateGraph = new StateGraph(() -> Arrays.stream(WorkflowRegisterKeys.values())
                .collect(Collectors.toMap(WorkflowRegisterKeys::getKey, WorkflowRegisterKeys::getKeyStrategy)))
                .addNode("workflowContextNode", node_async(workflowContextNode))
                .addNode("rankingContextNode", node_async(rankingContextNode))
                .addNode("toolRetrieverNode", node_async(toolRetrieverNode))
                .addNode("llmNode", node_async((llmNode)))
                .addEdge(START, "workflowContextNode")
                .addConditionalEdges("workflowContextNode", edge_async(state -> {
                    WorkflowContext workflowContext = (WorkflowContext) state.value(WorkflowRegisterKeys.WORKFLOW_CONTEXT_KEY.getKey())
                            .orElse(WorkflowContext.builder().nextNodeId(END).build());
                    return workflowContext.getNextNodeId();
                }), Map.of("rankingContextNode", "rankingContextNode", END, END))
                .addConditionalEdges("rankingContextNode", edge_async(state -> {
                    WorkflowContext workflowContext = (WorkflowContext) state.value(WorkflowRegisterKeys.WORKFLOW_CONTEXT_KEY.getKey())
                            .orElse(null);
                    List<String> toolNames = workflowContext.getToolNames();
                    if (CollectionUtils.isNotEmpty(toolNames)) {
                        return "llmNode";
                    }
                    return "toolRetrieverNode";
                }), Map.of("toolRetrieverNode", "toolRetrieverNode", "llmNode", "llmNode"))
                .addEdge("toolRetrieverNode", "llmNode")
                .addEdge("llmNode", END);
        GraphRepresentation graphRepresentation = stateGraph.getGraph(GraphRepresentation.Type.PLANTUML,
                "workflow graph");

        log.info("\n\n");
        log.info("workflow graph representation: {}", graphRepresentation.content());
        log.info("\n\n");
        return stateGraph.compile();
    }

}
