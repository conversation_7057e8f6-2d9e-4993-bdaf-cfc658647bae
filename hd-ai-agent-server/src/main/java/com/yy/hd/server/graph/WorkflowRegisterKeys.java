package com.yy.hd.server.graph;

import com.alibaba.cloud.ai.graph.KeyStrategy;
import com.alibaba.cloud.ai.graph.state.strategy.ReplaceStrategy;

public enum WorkflowRegisterKeys {

    REQUEST_CONTEXT_KEY("requestContext", new ReplaceStrategy()),
    TOOL_NAME_METADATA_KEY("toolName", new ReplaceStrategy()),
    MESSAGES_KEY("messages", new ReplaceStrategy()),
    WORKFLOW_CONTEXT_KEY("workflowContext", new ReplaceStrategy());

    private final String key;

    private final KeyStrategy keyStrategy;

    WorkflowRegisterKeys(String key, KeyStrategy keyStrategy) {
        this.key = key;
        this.keyStrategy = keyStrategy;
    }

    public String getKey() {
        return key;
    }

    public KeyStrategy getKeyStrategy() {
        return keyStrategy;
    }
}
