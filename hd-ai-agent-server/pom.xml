<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yy.hd</groupId>
        <artifactId>hd-ai-agent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>hd-ai-agent-server</artifactId>

    <properties>
        <udb.version>2.0.0</udb.version>
        <mysql-connector-j.version>9.0.0</mysql-connector-j.version>
        <spring-webflux-mdc.version>1.0.2</spring-webflux-mdc.version>
        <spring-ai-alibaba.version>1.0.0.2</spring-ai-alibaba.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-bom</artifactId>
                <version>${spring-ai-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>

        <dependency>
            <groupId>com.alibaba.cloud.ai</groupId>
            <artifactId>spring-ai-alibaba-graph-core</artifactId>
            <version>${spring-ai-alibaba.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-mcp-client-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-commons</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>spring-cloud-starter-hd-s2s-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>com.vincenzoracca</groupId>
            <artifactId>spring-webflux-mdc</artifactId>
            <version>${spring-webflux-mdc.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>${mysql-connector-j.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.huanju.udb.lgn</groupId>
            <artifactId>udb-client-intranet</artifactId>
            <version>${udb.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd-ai-agent-model</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd-ai-agent-commons</artifactId>
            <version>${project.version}</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>hd-ai-agent-server</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin><!-- 构造MANIFEST.MF文件 -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix><!-- 潜龙将使用此目录作为启动的classpath参数 -->
                            <mainClass>com.yy.hd.server.AgentServerApplication</mainClass><!-- 此处根据项目的Main类进行修改 -->
                            <useUniqueVersions>false</useUniqueVersions><!-- 解决snapshot的jar文件名与MANIFEST.MF文件中不一致的问题 -->
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies-runtime</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/classes/lib</outputDirectory>
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <!-- 排除二进制文件过滤 -->
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>onnx</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>