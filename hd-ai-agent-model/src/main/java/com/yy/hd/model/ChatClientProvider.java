package com.yy.hd.model;

import com.yy.hd.model.properties.ModelProviderProperties;
import org.springframework.ai.chat.client.ChatClient;

import java.util.Map;

public interface ChatClientProvider {

    /**
     * 大模型提供商配置映射
     * key: 提供商标识（如 openai, mistral）
     * value: 提供商配置
     */
    Map<String, ModelProviderProperties> getModelProviders();

    ChatClient getDefaultChatClient();

    ChatClient getChatClient(String provider, String model);
}
